#!/bin/bash

# Fix Terraform dependency cycle caused by module rename
# From: get-static-page-by-path-name
# To: get-static-page-url

echo "🔧 Fixing Terraform dependency cycle..."

# Step 1: Remove orphaned resources from state
echo "📝 Removing orphaned module from state..."

# These are the likely resource names that need to be removed
RESOURCES_TO_REMOVE=(
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name"
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name.module.gateway-resource"
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name.module.gateway-resource.aws_api_gateway_resource.resource"
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name.module.gateway-resource.aws_api_gateway_method.method"
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name.module.gateway-resource.aws_api_gateway_integration.integration"
    "module.common.module.auction-side.module.gateway-resource.module.get-static-page-by-path-name.module.gateway-resource.module.lambda-resource.aws_lambda_function.default-lambda"
)

# Try to remove each resource (some may not exist, that's OK)
for resource in "${RESOURCES_TO_REMOVE[@]}"; do
    echo "Trying to remove: $resource"
    terraform state rm "$resource" 2>/dev/null || echo "  (Resource not found or already removed)"
done

echo "✅ State cleanup complete!"

# Step 2: Apply the new configuration
echo "🚀 Applying new configuration..."
terraform apply -target=module.common.module.auction-side.module.gateway-resource.module.get-static-page-url -auto-approve

if [ $? -eq 0 ]; then
    echo "✅ Successfully applied new get-static-page-url module!"

    # Step 3: Apply the full configuration
    echo "🚀 Applying full configuration..."
    terraform apply -auto-approve

    if [ $? -eq 0 ]; then
        echo "🎉 Terraform dependency cycle resolved successfully!"
    else
        echo "❌ Full apply failed. You may need to run 'terraform apply' manually."
    fi
else
    echo "❌ Failed to apply new module. Please check the logs above."
fi
