data "aws_iam_policy_document" "s3-policy" {

  statement {

    effect = "Allow"

    actions = [
      "s3:*"
    ]

    resources = ["${var.s3-bucket-arn}/*"]

  }

}

resource "aws_iam_policy" "s3_policy" {
  name   = "${var.environment}-${var.project_name}-lambda-s3-policy"
  policy = data.aws_iam_policy_document.s3-policy.json
}

resource "aws_iam_role_policy_attachment" "lambda_s3_attach" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = aws_iam_policy.s3_policy.arn
}
