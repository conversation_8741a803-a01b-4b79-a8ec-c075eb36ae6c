const {
  HeadObjectCommand,
  GetObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3')
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner')
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body);
  const tenantId = Common.extractTenantId(e)
  const client = new S3Client({})

    const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => validator.validate(params))
    .then(() => {
      const command = new HeadObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: params.file_url,
      })
      return client.send(command)
    })
    .then(data => {
      console.log('File exists in S3:', data)

      // Generate pre-signed URL for the static page
      const getParams = {
        Bucket: process.env.S3_BUCKET,
        Key: params.file_url,
        ResponseContentType: 'text/html'  // Ensure HTML content type
      }

      const expiresIn = Number.parseInt(process.env.SIGNED_URL_EXPIRES || '3600', 10)
      const command = new GetObjectCommand(getParams)
      return getSignedUrl(client, command, {expiresIn})
    })
    .then(presignedUrl => {
      console.log('Generated pre-signed URL for static page')
      return base.createSuccessResponse(cb, {
        presigned_url: presignedUrl,
        file_url: params.file_url
      })
    })
    .catch(error => {
      console.error('Error generating pre-signed URL:', error)

      if (error.name === 'NoSuchKey' || error.name === 'NotFound') {
        const response = {
          status: 404,
          errors: {
            file_url: 'Static page file not found'
          }
        }
        return base.createErrorResponse(cb, response)
      }

      if (error.name === 'Forbidden' || error.code === 'Forbidden') {
        const response = {
          status: 403,
          errors: {
            file_url: 'Access denied to static page file'
          }
        }
        return base.createErrorResponse(cb, response)
      }

      return base.createErrorResponse(cb, error)
    })
}
