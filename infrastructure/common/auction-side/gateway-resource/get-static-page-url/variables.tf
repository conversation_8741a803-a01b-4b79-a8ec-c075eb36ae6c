variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "allow_origin" {
  description = "allow_origin"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "aws_api_gateway_rest_api_gateway_id" {
  description = "Aws api gateway rest api"
  default     = ""
}

variable "parent_id" {
  description = "Aws api gateway parent resource id"
  default     = ""
}

variable "parent_path" {
  description = "Aws api gateway parent resource path"
  default     = ""
}

variable "authorization" {
  description = "Authorization type"
  default     = "NONE"
}

variable "aws_api_gateway_authorizer_id" {
  description = "Aws api gateway authorizer id"
  default     = ""
}

variable "prefix_function_name" {
  description = "Prefix function name"
}

variable "lambda_layer" {
  description = "Lambda layer"
}

variable "lambda_subnet_ids" {
  description = "Lambda subnet ids"
}

variable "lambda_security_group_id" {
  description = "Lambda security group id"
}

variable "lambda_global_environment_variables" {
  description = "Lambda global environment variables"
}

variable "slack-notification-lambda-arn" {
  description = "Slack notification lambda arn"
}

variable "aws_api_gateway_rest_api_gateway_execution_arn" {
  description = "Aws api gateway rest api gateway execution arn"
}
