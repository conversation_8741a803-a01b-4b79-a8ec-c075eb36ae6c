// const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
// const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
// const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js')
// const pool = new PgPool(process.env.READ_ONLY_PGHOST)
// const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

// exports.handle = function (e, ctx, cb) {
//   const params = Common.parseRequestBody(e.body);
//   console.log('🔑params = ' + JSON.stringify(params))
//   const tenantNo = Common.extractTenantId(e);

//   const base = new Base(pool, params.languageCode);
//   const validator = new Validator(base);

// console.log('⛳️ log of tenantNo : ', tenantNo)

//   ctx.callbackWaitsForEmptyEventLoop = false


//   Promise.resolve()
//     .then(() => base.startRequest(e))
//     .then(() => validator.validate(params))
//     .then(() => {
//       const sql = 'select * from f_get_static_page_by_path($1,$2)'
//       const sql_params = [tenantNo, params['page_path']]

//       console.log('sql = ' + JSON.stringify(sql))
//       console.log('sql_params = ' + JSON.stringify(sql_params))

//       return pool.rlsQuery(tenantNo, sql, sql_params)
//     })
//     .then(page => {
//       console.log('✅️page = ' + JSON.stringify(page))

//       return base.createSuccessResponse(cb, page)
//     })
//     .catch(error => base.createErrorResponse(cb, error))
// }
