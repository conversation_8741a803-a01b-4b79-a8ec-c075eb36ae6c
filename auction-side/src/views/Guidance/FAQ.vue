<script setup>
  import useApi from '@/composables/useApi'
  import {useTenantSettingsStore} from '@/stores/tenantSettings'
  import {useLoaderStore} from '@/stores/ui'
  import $ from 'jquery'
  import {computed, nextTick, onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'

  const {current} = useLocale()
  const tenantSettingsStore = useTenantSettingsStore()
  const {apiExecute} = useApi()

  const currentLocale = ref(current.value)
  const htmlContent = ref('')
  const loader = useLoaderStore()
  const errorMessage = ref('')

  const profilePage = computed(() => {
    return tenantSettingsStore.getStaticPage('faq')
  })

  const currentLanguageContent = computed(() => {
    if (!profilePage.value) return null
    return profilePage.value.localized.find(
      localized => localized.language_code === currentLocale.value
    )
  })

  const getPresignedUrl = async fileUrl => {
    try {
      console.log(`Getting pre-signed URL for: ${fileUrl}`)
      const response = await apiExecute('public/get-static-page-url', {
        file_url: fileUrl,
      })
      if (response && response.presigned_url) {
        return response.presigned_url
      } else {
        throw new Error('No pre-signed URL returned from API')
      }
    } catch (err) {
      console.error('Error getting pre-signed URL:', err)
      throw new Error(`Failed to get file access: ${err.message}`)
    }
  }

  const fetchHtmlContent = async () => {
    if (!currentLanguageContent.value?.file_url) {
      console.warn(`No content found for language: ${currentLocale.value}`)
      errorMessage.value = `No content available for language: ${currentLocale.value}`
      return
    }

    loader.setLoading(true)
    errorMessage.value = ''

    try {
      const presignedUrl = await getPresignedUrl(currentLanguageContent.value.file_url)
      const response = await fetch(presignedUrl)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const content = await response.text()
      htmlContent.value = content
      console.log('Successfully fetched HTML content using pre-signed URL')

      // Initialize jQuery accordion after HTML is rendered
      await nextTick() // Wait for DOM to update
      initializeAccordion()
    } catch (err) {
      console.error('Error fetching HTML content:', err)
      errorMessage.value = `Failed to load content: ${err.message}`
      htmlContent.value = ''
    } finally {
      loader.setLoading(false)
    }
  }

  // FAQ Accordion Functionality (jQuery)
  const initializeAccordion = () => {
    const accordionDetails = '.js-details'
    const accordionSummary = '.js-details-summary'
    const accordionContent = '.js-details-content'
    const speed = 300

    $(accordionSummary).each(function () {
      $(this)
        .off('click')
        .on('click', function (event) {
          event.preventDefault()

          // Nếu accordion này đã mở thì bỏ qua (không đóng lại)
          if ($(this).parent(accordionDetails).attr('open')) {
            return // Không làm gì cả
          }

          // Mở accordion (expand-only)
          $(this).addClass('is-active')
          $(this).parent(accordionDetails).attr('open', 'true')
          $(this).nextAll(accordionContent).hide().slideDown(speed)
        })
    })
  }

  const handleLanguageChange = async () => {
    console.log(`Language changed to: ${currentLocale.value}`)
    await fetchHtmlContent()
  }

  watch(
    () => current.value,
    newLocale => {
      currentLocale.value = newLocale
      handleLanguageChange()
    }
  )

  watch(
    () => tenantSettingsStore.isLoaded,
    isLoaded => {
      if (isLoaded) {
        fetchHtmlContent()
      }
    }
  )

  onMounted(async () => {
    if (tenantSettingsStore.isLoaded) {
      await fetchHtmlContent()
    }
  })
</script>

<template>
  <div v-if="errorMessage" class="error-container">
    <v-alert type="error" variant="outlined">
      {{ errorMessage }}
    </v-alert>
  </div>
  <div v-else-if="htmlContent" class="dynamic-content" v-html="htmlContent" />
</template>

<style scoped>
  /* Same styles as in your original code, with additional accordion styles */
  .dynamic-content :deep(.js-details-content) {
    display: none; /* Ensure content is hidden initially for jQuery slideDown */
  }

  .dynamic-content :deep(.js-details[open] .js-details-content) {
    display: block;
  }

  .dynamic-content :deep(.js-details-summary) {
    cursor: pointer;
  }

  .dynamic-content :deep(.js-details-summary.is-active) {
  }
</style>
