<script setup>
  import useApi from '@/composables/useApi'
  import {useTenantSettingsStore} from '@/stores/tenantSettings'
  import {computed, onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'

  const {current} = useLocale()
  const tenantSettingsStore = useTenantSettingsStore()
  const {apiExecute} = useApi()

  // Reactive state
  const currentLocale = ref(current.value)
  const htmlContent = ref('')
  const isLoading = ref(false)
  const error = ref('')

  // Computed property to get the profile static page
  const profilePage = computed(() => {
    return tenantSettingsStore.getStaticPage('profile')
  })

  // Computed property to get the current language content
  const currentLanguageContent = computed(() => {
    if (!profilePage.value) return null

    return profilePage.value.localized.find(
      localized => localized.language_code === currentLocale.value
    )
  })

  /**
   * Get pre-signed URL for the static page file
   * This replaces direct S3 access to handle private bucket permissions
   */
  const getPresignedUrl = async fileUrl => {
    try {
      console.log(`Getting pre-signed URL for: ${fileUrl}`)

      const response = await apiExecute('public/get-static-page-url', {
        file_url: fileUrl,
      })

      if (response && response.presigned_url) {
        return response.presigned_url
      } else {
        throw new Error('No pre-signed URL returned from API')
      }
    } catch (err) {
      console.error('Error getting pre-signed URL:', err)
      throw new Error(`Failed to get file access: ${err.message}`)
    }
  }

  /**
   * Fetch HTML content using pre-signed URL approach
   * Two-step process:
   * 1. Get pre-signed URL from API
   * 2. Fetch content using that URL
   */
  const fetchHtmlContent = async () => {
    if (!currentLanguageContent.value?.file_url) {
      console.warn(`No content found for language: ${currentLocale.value}`)
      error.value = `No content available for language: ${currentLocale.value}`
      return
    }

    isLoading.value = true
    error.value = ''

    try {
      // Step 1: Get pre-signed URL from API endpoint
      const presignedUrl = await getPresignedUrl(currentLanguageContent.value.file_url)
      console.log('Successfully obtained pre-signed URL')

      // Step 2: Fetch HTML content using the pre-signed URL
      const response = await fetch(presignedUrl)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const content = await response.text()
      htmlContent.value = content
      console.log('Successfully fetched HTML content using pre-signed URL')
    } catch (err) {
      console.error('Error fetching HTML content:', err)
      error.value = `Failed to load content: ${err.message}`
      htmlContent.value = ''
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Handle language change
   */
  const handleLanguageChange = async () => {
    console.log(`Language changed to: ${currentLocale.value}`)
    await fetchHtmlContent()
  }

  // Watch for locale changes
  watch(
    () => current.value,
    newLocale => {
      currentLocale.value = newLocale
      handleLanguageChange()
    }
  )

  // Watch for tenant settings changes (in case they load after component mount)
  watch(
    () => tenantSettingsStore.isLoaded,
    isLoaded => {
      if (isLoaded) {
        fetchHtmlContent()
      }
    }
  )

  // Initialize on mount
  onMounted(async () => {
    console.log('CompanyProfile component mounted')

    // If tenant settings are already loaded, fetch content immediately
    if (tenantSettingsStore.isLoaded) {
      await fetchHtmlContent()
    }
    // Otherwise, the watcher will handle it when tenant settings load
  })
</script>

<template>
  <div class="company-profile">
    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <!-- <div class="loading-spinner">
        <v-progress-circular indeterminate color="primary" />
      </div>
      <p>Loading content...</p> -->
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-container">
      <!-- <v-alert type="error" variant="outlined">
        {{ error }}
      </v-alert> -->
      <!-- Fallback content -->
      <!-- <div class="fallback-content">
        <h1>Company Profile</h1>
        <p>Content is temporarily unavailable. Please check your connection and try again later.</p>
      </div> -->
    </div>

    <!-- Dynamic HTML content -->
    <div v-else-if="htmlContent" class="dynamic-content" v-html="htmlContent" />

    <!-- No content available -->
    <div v-else class="no-content">
      <h1>Company Profile</h1>
      <p>No content available for the selected language.</p>
    </div>
  </div>
</template>

<style scoped>
  .company-profile {
    min-height: 200px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .loading-spinner {
    margin-bottom: 1rem;
  }

  .error-container {
    padding: 1rem;
  }

  .fallback-content,
  .no-content {
    padding: 2rem;
    text-align: center;
  }

  .dynamic-content {
    /* Allow the dynamic content to inherit styles from the parent */
    line-height: 1.6;
  }

  /* Ensure dynamic content styles are properly applied */
  .dynamic-content :deep(h1),
  .dynamic-content :deep(h2),
  .dynamic-content :deep(h3) {
    margin-bottom: 1rem;
  }

  .dynamic-content :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }

  .dynamic-content :deep(th),
  .dynamic-content :deep(td) {
    padding: 0.5rem;
    border: 1px solid #ddd;
    text-align: left;
  }

  .dynamic-content :deep(th) {
    background-color: #f5f5f5;
    font-weight: bold;
  }
</style>
