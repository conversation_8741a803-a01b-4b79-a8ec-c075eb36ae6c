@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* アニメーション
 * *========================================== */
@-webkit-keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ボタン
 * *********************************************************************** */
/* 基本形ボタン
 * *========================================== */
[class^=btnBsc-] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 60px;
  border-radius: 100vh;
  margin: 0 auto;
  padding: 0;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  width: 100%;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  [class^=btnBsc-] {
    height: 50px;
    font-size: 14px;
  }
}
[class^=btnBsc-]:hover {
  opacity: 0.8;
}
[class^=btnBsc-] img {
  display: inline-block;
}

/* リスト下ボタン */
main section .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
main section .wrap-btn .list-more {
  width: 300px;
  height: 50px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #bf2a24;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn .list-more {
    height: 60px;
  }
}

/* 退会ボタン */
.container .wrap-btn {
  margin: 0 0 60px;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  .container .wrap-btn {
    margin: 14vw 0 7vw;
    padding: 0;
  }
}
.container .wrap-btn button.withdraw {
  width: 280px;
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
  color: #e98181;
  font-size: 1rem;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #e98181;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  .container .wrap-btn button.withdraw {
    width: 100%;
    height: 14vw;
    font-size: 4vw;
  }
}
.container .wrap-btn button.withdraw:hover {
  opacity: 0.8;
}

/* ---------------------------
 * *ダウンロードボタン
 * *----------------------------- */
.btnBsc-DL {
  background-color: #427fae;
}
.btnBsc-DL img {
  width: 24px;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  .btnBsc-DL img {
    width: 18px;
  }
}

/* ---------------------------
 * *黒（濃グレー）ボタン
 * *----------------------------- */
.btnBsc-Black {
  background-color: #333;
}
.btnBsc-Black img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-Black img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *色ボタン（コーポレートカラー）
 * *----------------------------- */
.btnBsc-CoCor {
  background-color: #427fae;
}
.btnBsc-CoCor img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-CoCor img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *アクションボタン（会員登録など）
 * *----------------------------- */
.btn-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 40px;
  width: 100%;
  text-align: center;
  margin: 60px 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  .btn-form {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 7vw;
    margin: 10vw 0;
  }
}
.btn-form .btn.back {
  color: #427fae;
  font-size: 1rem;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #427fae;
}
@media screen and (max-width: 767px) {
  .btn-form .btn.back {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}
@media screen and (max-width: 767px) {
  .btn-form .btn.entry {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
}

/* ---------------------------
 * *リンク
 * *----------------------------- */
a.link-std {
  color: #427fae;
  text-decoration: underline;
}
a.link-std:hover {
  text-decoration: none;
  opacity: 1;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *お気に入りマーク
 * *********************************************************************** */
.com-item-box {
  display: inline-block;
  position: relative;
}
.com-item-box p.fav-mark {
  display: block;
  background: url("../img/common/icn_favorite_detail.svg") center 7px no-repeat;
  background-size: 22px 22px;
  width: 70px;
  margin: 0 0 0 1rem;
  padding: 23px 5px 2px;
  text-align: center;
  border: 1px solid #e9eaeb;
  border-radius: 4px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  .com-item-box p.fav-mark {
    background: url("../img/common/icn_favorite_detail.svg") center 1.8vw no-repeat;
    background-size: 5vw 5vw;
    width: 16vw;
    margin: 0 0 0 4vw;
    padding: 6.3vw 1vw 0.2vw;
    border-radius: 1vw;
  }
}
.com-item-box p.fav-mark span {
  color: #333;
  font-size: 10px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  .com-item-box p.fav-mark span {
    font-size: 2.2vw;
  }
}
.com-item-box p.fav-mark.active {
  background-image: url("../img/common/icn_favorite_blue.svg");
  border: 1px solid #427fae;
}
.com-item-box p.fav-mark.active span {
  color: #427fae;
}
.com-item-box p.fav-mark:hover {
  background-image: url("../img/common/icn_favorite_blue.svg");
}

/* ログイン前は非表示 */
body.state-out span.fav-mark {
  display: none !important;
}
body.item_p-detail #terms.com-item-box {
  display: block;
}
body.item_p-detail #terms.com-item-box span.fav-mark {
  width: 40px;
  height: 40px;
  right: 20px;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *見出し
 * *********************************************************************** */
#main h2.page-ttl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  margin: 0;
  padding: 2.5rem 1rem 1.5rem;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl {
    margin: 0;
    padding: 6vw 4vw 2vw;
  }
}
#main h2.page-ttl .ttl {
  width: 100%;
  text-align: center;
  margin: 0.5rem 0;
  padding: 0;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: 2px;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl .ttl {
    margin: 0 0 0.5rem;
    font-size: 5.3vw;
    font-weight: 600;
  }
}
#main h2.page-ttl .ttl .red {
  color: #427fae;
}
#main h2.page-ttl .sub {
  margin: 0 0 0.2rem;
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  letter-spacing: 2px;
  font-family: "Noto Sans JP", sans-serif;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl .sub {
    margin: 0 0 2vw;
    padding: 0 2vw;
    font-size: 2.5vw;
  }
}
#main h2.page-ttl .ttl.specified {
  font-size: 1.2rem;
}
#main h2.page-ttl.list {
  height: 80px;
  margin: 40px 0 0;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl.list {
    height: 14vw;
    margin: 7vw 0 0;
  }
}
#main h3 {
  margin: 4rem 0 3rem;
  padding: 0;
  font-size: 2.2rem;
  font-weight: 600;
  line-height: 1.2;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main h3 {
    font-size: 5.5vw;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ページトップ
 * *********************************************************************** */
#page_top {
  position: fixed;
  display: block;
  width: 42px;
  height: 42px;
  right: 1rem;
  bottom: 1.2rem;
  background-color: rgba(9, 46, 86, 0.5);
  border-radius: 50%;
  z-index: 10;
}
#page_top a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 11;
}
#page_top a:hover {
  opacity: 0.7;
}
#page_top a:before {
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% - 4px);
  margin-top: -4px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
/*# sourceMappingURL=parts.css.map */