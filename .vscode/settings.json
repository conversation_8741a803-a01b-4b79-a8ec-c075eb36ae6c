{"files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "editor.autoIndent": "full", "editor.tabSize": 2, "editor.indentSize": 2, "files.eol": "\n", "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "html.format.wrapAttributes": "force-expand-multiline", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "svg.preview.background": "dark-transparent", "cSpell.words": ["<PERSON><PERSON>", "axios", "<PERSON><PERSON>", "eips", "favorited", "fuin", "<PERSON><PERSON><PERSON>", "iconsets", "mainte", "MAKESHOP", "pdfs", "pems", "persistedstate", "PGHOST", "photoswipe", "plpgsql", "postar", "preauc", "psql", "regist", "sank<PERSON>igen", "satei", "seri", "shuppin", "SOLDOUT", "TEIF", "TIAF", "TOKUSHO", "tokushoho", "to<PERSON><PERSON>", "unslick", "VITE", "vuetify"], "references.preferredLocation": "view", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}